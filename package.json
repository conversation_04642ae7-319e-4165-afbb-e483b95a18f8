{"name": "editpal", "version": "2025.08.28", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate --config=drizzle-dev.config.ts", "db:migrate:dev": "drizzle-kit migrate --config=drizzle-dev.config.ts", "db:migrate:turso-prod": "drizzle-kit migrate --config=drizzle-prod.config.ts", "docker:build": "docker compose --file=compose.prod.yml build", "docker:push": "docker push ghcr.io/notlandingstudio/editpal-next:${npm_config_tag}"}, "dependencies": {"@ai-sdk/openai": "^1.3.20", "@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@fal-ai/client": "^1.5.0", "@floating-ui/react": "^0.27.13", "@hookform/resolvers": "^4.1.3", "@libsql/client": "^0.15.4", "@lucide/lab": "^0.1.2", "@next/third-parties": "^15.3.5", "@orpc/client": "^1.8.8", "@orpc/server": "^1.8.8", "@orpc/tanstack-query": "^1.8.8", "@polar-sh/nextjs": "^0.3.23", "@polar-sh/sdk": "^0.32.4", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.87.4", "@tiptap/extension-blockquote": "^2.25.0", "@tiptap/extension-bold": "^2.25.0", "@tiptap/extension-bullet-list": "^2.25.0", "@tiptap/extension-code-block": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-document": "^2.25.0", "@tiptap/extension-heading": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-list-item": "^2.25.0", "@tiptap/extension-ordered-list": "^2.25.0", "@tiptap/extension-paragraph": "^2.25.0", "@tiptap/extension-subscript": "^2.25.0", "@tiptap/extension-superscript": "^2.25.0", "@tiptap/extension-table-of-contents": "^2.25.0", "@tiptap/extension-task-item": "^2.25.0", "@tiptap/extension-task-list": "^2.25.0", "@tiptap/extension-text": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-typography": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "@uidotdev/usehooks": "^2.4.1", "@upstash/redis": "^1.34.9", "ai": "^4.3.10", "better-auth": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "embla-carousel-react": "^8.5.2", "i18next": "^23.7.12", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "jose": "^6.0.10", "konva": "^10.0.2", "libsodium-wrappers": "^0.7.15", "lodash": "^4.17.21", "lodash.throttle": "^4.1.1", "lucide-react": "^0.525.0", "motion": "^12.23.12", "nanoid": "^5.1.5", "next": "^15.3.3", "next-client-cookies": "^2.0.1", "next-i18n-router": "^5.5.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "ofetch": "^1.4.1", "react": "^19.1.0", "react-compare-image": "^3.5.4", "react-cookie": "^8.0.1", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "react-i18next": "^15.4.1", "react-konva": "^19.0.10", "react-konva-utils": "^2.0.0", "react-markdown": "^10.1.0", "react-microsoft-clarity": "^2.0.0", "react-tweet": "^3.2.2", "react-use-measure": "^2.1.7", "sonner": "^2.0.2", "superjson": "^2.2.2", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tiptap-extension-resize-image": "^1.2.2", "url-slug": "^4.0.1", "use-image": "^1.1.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@types/libsodium-wrappers": "^0.7.14", "@types/lodash": "^4.17.16", "@types/lodash.throttle": "^4.1.9", "@types/node": "^20", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "@wavesurfer/react": "^1.0.11", "drizzle-kit": "^0.30.6", "eslint": "^9.27.0", "eslint-config-next": "15.3.3", "eslint-plugin-react-you-might-not-need-an-effect": "^0.0.43", "postcss": "^8.4.38", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.12", "react-medium-image-zoom": "^5.2.14", "sass": "^1.89.2", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.1.6"}}