"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { motion } from "motion/react";
import { cn } from "@/lib/utils";

interface ImageComparisonSliderProps {
	beforeImage: string;
	beforeImageAlt: string;
	afterImage: string;
	afterImageAlt: string;
	className?: string;
}

export function ImageComparisonSlider({ beforeImage, beforeImageAlt, afterImage, afterImageAlt, className }: ImageComparisonSliderProps) {
	const containerRef = useRef<HTMLDivElement>(null);
	const [sliderPosition, setSliderPosition] = useState(50); // 初始位置在中间
	const [isUserInteracting, setIsUserInteracting] = useState(false);

	// 处理鼠标/触摸事件
	const handlePointerMove = useCallback(
		(e: MouseEvent | TouchEvent) => {
			if (!containerRef.current || !isUserInteracting) return;

			const rect = containerRef.current.getBoundingClientRect();
			const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
			const x = clientX - rect.left;
			const newPosition = Math.max(0, Math.min(100, (x / rect.width) * 100));
			setSliderPosition(newPosition);
		},
		[isUserInteracting],
	);

	const handlePointerUp = useCallback(() => {
		setIsUserInteracting(false);
		// 延迟恢复自动动画，给用户一些时间观察
		setTimeout(() => setIsUserInteracting(false), 3000);
	}, []);

	useEffect(() => {
		if (isUserInteracting) {
			document.addEventListener("mousemove", handlePointerMove);
			document.addEventListener("mouseup", handlePointerUp);
			document.addEventListener("touchmove", handlePointerMove);
			document.addEventListener("touchend", handlePointerUp);
		}

		return () => {
			document.removeEventListener("mousemove", handlePointerMove);
			document.removeEventListener("mouseup", handlePointerUp);
			document.removeEventListener("touchmove", handlePointerMove);
			document.removeEventListener("touchend", handlePointerUp);
		};
	}, [isUserInteracting, handlePointerMove, handlePointerUp]);

	// 自动动画效果
	useEffect(() => {
		if (isUserInteracting) return;

		let timeoutId: NodeJS.Timeout;
		let currentStep = 0;

		const animationSteps = [
			{ position: 50, duration: 1000 }, // 初始位置，等待1秒
			{ position: 85, duration: 2500 }, // 向右移动到最右边，停留2.5秒
			{ position: 15, duration: 2500 }, // 向左移动到最左边，停留2.5秒
		];

		const executeStep = () => {
			if (isUserInteracting) return;

			const step = animationSteps[currentStep];
			setSliderPosition(step.position);

			timeoutId = setTimeout(() => {
				if (!isUserInteracting) {
					// 第一步之后，在右边和左边之间循环
					if (currentStep === 0) {
						currentStep = 1; // 从初始位置到右边
					} else if (currentStep === 1) {
						currentStep = 2; // 从右边到左边
					} else {
						currentStep = 1; // 从左边回到右边，循环
					}
					executeStep();
				}
			}, step.duration);
		};

		// 开始动画
		executeStep();

		return () => {
			if (timeoutId) clearTimeout(timeoutId);
		};
	}, [isUserInteracting]);

	return (
		<div ref={containerRef} className={cn("relative aspect-[3/2] cursor-grab overflow-hidden rounded-xl border active:cursor-grabbing", className)}>
			{/* After Image (底层) */}
			<div className="absolute inset-0">
				<img src={afterImage} alt={afterImageAlt} className="h-full w-full object-cover" draggable={false} loading="lazy" />
			</div>

			{/* Before Image (上层，通过 clip-path 控制显示区域) */}
			<motion.div
				className="absolute inset-0"
				initial={{
					clipPath: `polygon(0 0, 50% 0, 50% 100%, 0 100%)`,
				}}
				animate={{
					clipPath: `polygon(0 0, ${sliderPosition}% 0, ${sliderPosition}% 100%, 0 100%)`,
				}}
				transition={{ duration: isUserInteracting ? 0 : 0.8, ease: "easeInOut" }}
			>
				<img src={beforeImage} alt={beforeImageAlt} className="h-full w-full object-cover" draggable={false} loading="lazy" />
			</motion.div>

			{/* 滑块控制器 */}
			<motion.div
				className="absolute top-0 bottom-0 z-10 w-1 cursor-grab bg-white shadow-lg active:cursor-grabbing"
				initial={{
					left: "50%",
				}}
				animate={{
					left: `${sliderPosition}%`,
				}}
				transition={{ duration: isUserInteracting ? 0 : 0.8, ease: "easeInOut" }}
				style={{
					transform: "translateX(-50%)",
				}}
				onMouseDown={(e: React.MouseEvent) => {
					e.preventDefault();
					setIsUserInteracting(true);
				}}
				onTouchStart={(e: React.TouchEvent) => {
					e.preventDefault();
					setIsUserInteracting(true);
				}}
			>
				{/* 滑块手柄 */}
				<div className="absolute top-1/2 left-1/2 flex h-8 w-8 -translate-x-1/2 -translate-y-1/2 transform items-center justify-center rounded-full border-2 border-gray-300 bg-white shadow-lg">
					<div className="h-4 w-1 rounded bg-gray-400"></div>
				</div>
			</motion.div>

			{/* 标签 */}
			<div className="absolute top-4 left-4 rounded bg-black/50 px-2 py-1 text-xs font-medium text-white">Before</div>
			<div className="absolute top-4 right-4 rounded bg-black/50 px-2 py-1 text-xs font-medium text-white">After</div>
		</div>
	);
}
