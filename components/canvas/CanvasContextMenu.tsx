import React from "react";
import {
	ContextMenuContent,
	ContextMenuItem,
	ContextMenuSub,
	ContextMenuSubTrigger,
	ContextMenuSubContent,
	ContextMenuSeparator,
} from "@/components/ui/context-menu";
import {
	Play,
	Copy,
	Crop,
	Scissors,
	Combine,
	Download,
	X,
	Layers,
	ChevronUp,
	ChevronDown,
	MoveUp,
	MoveDown,
	LoaderCircleIcon,
	ZoomIn,
	ZoomOut,
	Maximize2,
} from "lucide-react";
import type { PlacedImage, GenerationSettings } from "@/@types/canvas";
import { checkOS } from "@/lib/utils-os";

interface CanvasContextMenuProps {
	selectedIds: string[];
	images: PlacedImage[];
	handleDuplicate: () => void;
	handleRemoveBackground: () => void;
	handleCombineImages: () => void;
	handleDelete: () => void;
	setCroppingImageId: (id: string | null) => void;
	sendToFront: () => void;
	sendToBack: () => void;
	bringForward: () => void;
	sendBackward: () => void;
	onZoomIn?: () => void;
	onZoomOut?: () => void;
	onZoomToFit?: () => void;
}

export const CanvasContextMenu: React.FC<CanvasContextMenuProps> = ({
	selectedIds,
	images,
	handleDuplicate,
	handleRemoveBackground,
	handleCombineImages,
	handleDelete,
	setCroppingImageId,
	sendToFront,
	sendToBack,
	bringForward,
	sendBackward,
	onZoomIn,
	onZoomOut,
	onZoomToFit,
}) => {
	const isMac = checkOS("Mac");
	const modifierKey = isMac ? "⌘" : "Ctrl";
	return (
		<ContextMenuContent className="min-w-[200px] space-y-1 p-2">
			<ContextMenuItem
				onClick={handleDuplicate}
				disabled={selectedIds.length === 0}
				className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
			>
				Duplicate
			</ContextMenuItem>
			<ContextMenuItem
				onClick={() => {
					if (selectedIds.length === 1) {
						setCroppingImageId(selectedIds[0]);
					}
				}}
				disabled={selectedIds.length !== 1}
				className="flex w-full cursor-pointer items-center gap-2 text-xs text-neutral-600"
			>
				Crop
			</ContextMenuItem>
			<ContextMenuItem
				onClick={handleRemoveBackground}
				disabled={selectedIds.length === 0}
				className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
			>
				Remove background
			</ContextMenuItem>
			<ContextMenuItem
				onClick={handleCombineImages}
				disabled={selectedIds.length < 2}
				className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
			>
				Combine Images
			</ContextMenuItem>
			{selectedIds.length > 0 && (
				<>
					<ContextMenuItem
						onClick={bringForward}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Move up</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"]"}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendBackward}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Move down</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"["}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendToFront}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Bring to front</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"]"}</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem
						onClick={sendToBack}
						disabled={selectedIds.length === 0}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Send to back</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{"["}</div>
						</div>
					</ContextMenuItem>
				</>
			)}
			{selectedIds.length === 0 && (
				<>
					<ContextMenuSeparator />
					<ContextMenuItem onClick={onZoomIn} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom in</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">+</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={onZoomOut} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom out</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">-</div>
						</div>
					</ContextMenuItem>
					<ContextMenuItem onClick={onZoomToFit} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<span>Zoom to fit</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">⇧</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">1</div>
						</div>
					</ContextMenuItem>
				</>
			)}

			{selectedIds.length > 0 && (
				<>
					<ContextMenuSeparator />
					<ContextMenuItem
						onClick={async () => {
							for (const id of selectedIds) {
								const image = images.find((img) => img.id === id);

								if (image) {
									const link = document.createElement("a");
									link.download = `image-${Date.now()}.png`;
									link.href = image.src;
									link.click();
								}
							}
						}}
						className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
					>
						Download
					</ContextMenuItem>
					<ContextMenuItem onClick={handleDelete} className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600">
						Delete
					</ContextMenuItem>
				</>
			)}
		</ContextMenuContent>
	);
};
