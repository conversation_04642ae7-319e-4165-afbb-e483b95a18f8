import React, { useState } from "react";
import Link from "next/link";
import { Logo } from "../logo";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ChevronUp, ChevronDown, Home, ZoomIn, ZoomOut, Undo, Redo, Fullscreen } from "lucide-react";
import { checkOS } from "@/lib/utils-os";

interface HeaderDropdownProps {
	onZoomIn?: () => void;
	onZoomOut?: () => void;
	onZoomToFit?: () => void;
	onUndo?: () => void;
	onRedo?: () => void;
	canUndo?: boolean;
	canRedo?: boolean;
}

export const HeaderDropdown: React.FC<HeaderDropdownProps> = ({ onZoomIn, onZoomOut, onZoomToFit, onUndo, onRedo, canUndo = false, canRedo = false }) => {
	const [isOpen, setIsOpen] = useState(false);
	const [showHomeConfirm, setShowHomeConfirm] = useState(false);
	const isMac = checkOS("Mac");
	const modifierKey = isMac ? "⌘" : "Ctrl";

	return (
		<div className="absolute top-4 left-4 z-20">
			<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
				<DropdownMenuTrigger asChild>
					<button className="flex flex-row items-center gap-2 rounded-lg p-2 backdrop-blur-lg">
						<Logo className="size-6" />
						{isOpen ? <ChevronDown className="text-muted-foreground size-4" /> : <ChevronUp className="text-muted-foreground size-4" />}
					</button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="start" className="w-48 space-y-1 p-2">
					<AlertDialog open={showHomeConfirm} onOpenChange={setShowHomeConfirm}>
						<AlertDialogTrigger asChild>
							<DropdownMenuItem
								onSelect={(e) => e.preventDefault()}
								className="flex w-full cursor-pointer items-center gap-2 py-[8.5px] text-xs text-neutral-600"
							>
								<Home className="h-4 w-4" />
								<span>Home</span>
							</DropdownMenuItem>
						</AlertDialogTrigger>
						<AlertDialogContent className="sm:max-w-md">
							<AlertDialogHeader>
								<AlertDialogTitle>Leave current page?</AlertDialogTitle>
								<AlertDialogDescription>
									Your current work will not be saved. Are you sure you want to go to the home page?
								</AlertDialogDescription>
							</AlertDialogHeader>
							<AlertDialogFooter>
								<AlertDialogCancel>Cancel</AlertDialogCancel>
								<AlertDialogAction asChild>
									<Link href="/">Continue</Link>
								</AlertDialogAction>
							</AlertDialogFooter>
						</AlertDialogContent>
					</AlertDialog>
					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={onUndo}
						disabled={!canUndo}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<Undo className="h-4 w-4" />
							<span>Undo</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">Z</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={onRedo}
						disabled={!canRedo}
						className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs"
					>
						<div className="flex items-center gap-2 text-neutral-600">
							<Redo className="h-4 w-4" />
							<span>Redo</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{isMac ? "⇧" : "Shift"}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">Z</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuSeparator />
					<DropdownMenuItem onClick={onZoomToFit} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<Fullscreen className="h-4 w-4" />
							<span>Zoom to fit</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">⇧</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">1</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuItem onClick={onZoomIn} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<ZoomIn className="h-4 w-4" />
							<span>Zoom in</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">+</div>
						</div>
					</DropdownMenuItem>
					<DropdownMenuItem onClick={onZoomOut} className="flex w-full cursor-pointer items-center justify-between gap-2 py-[8.5px] text-xs">
						<div className="flex items-center gap-2 text-neutral-600">
							<ZoomOut className="h-4 w-4" />
							<span>Zoom out</span>
						</div>
						<div className="flex items-center gap-0.5 text-neutral-400">
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">{modifierKey}</div>
							<div className="flex h-[15px] min-w-[13px] items-center justify-center rounded-[2px] text-center">-</div>
						</div>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
};
