import { ChevronDownIcon } from "lucide-react";

type Faq = {
	question: string;
	answer: string;
};

export default function FAQsComponent({ title, faqs }: { title?: string; faqs: Faq[] }) {
	return (
		<div id="faq" className="px-4 py-24 md:px-6">
			<div className="bg-muted container flex flex-col items-center justify-center gap-10 rounded-2xl px-4 py-16 md:rounded-3xl md:px-6">
				<div className="max-w-4xl text-center">
					<h2 className="text-3xl font-semibold text-pretty">{title ?? "Frequently Asked Questions"}</h2>
				</div>
				<div className="w-full max-w-4xl">
					<div className="space-y-2 divide-y">
						{faqs.map((faq, index) => (
							<details key={index} className="group" open={false}>
								<summary className="flex cursor-pointer list-none items-center justify-between py-4 text-lg font-medium group-open:text-blue-500 group-hover:text-blue-500 hover:cursor-pointer">
									<h3 className="">{faq.question}</h3>
									<ChevronDownIcon
										className="pointer-events-none size-[18px] shrink-0 translate-y-0.5 transition-transform duration-200 group-open:rotate-180"
										strokeWidth={2.5}
									/>
								</summary>
								<div className="text-secondary-foreground grid grid-rows-[0fr] overflow-hidden pb-4 transition-[grid-template-rows] duration-300 ease-in-out group-open:grid-rows-[1fr]">
									<div className="min-h-0">
										<p className="whitespace-pre-wrap">{faq.answer}</p>
									</div>
								</div>
							</details>
						))}
					</div>
				</div>
			</div>
		</div>
	);
}
