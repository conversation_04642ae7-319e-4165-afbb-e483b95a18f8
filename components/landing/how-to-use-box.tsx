"use client";

import React, { ComponentProps } from "react";
import { cn } from "@/lib/utils";

export function HowToUseBox({
	title,
	description,
	steps,
	...props
}: {
	title?: string;
	description?: string;
	steps: {
		title: string;
		description?: string;
		url?: string;
	}[];
} & ComponentProps<"div">) {
	return (
		<div className="px-0 py-24 md:px-6">
			<div className="dark container flex flex-col items-center gap-16 bg-blue-500 px-4 py-16 md:rounded-2xl md:px-6" {...props}>
				<div className="text-center">
					<h2 className="text-3xl font-semibold text-pretty text-white">{title}</h2>
					{description && <p className="mx-auto mt-4 max-w-4xl text-pretty text-white">{description}</p>}
				</div>

				<div
					className={cn(
						"grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-8",
						steps.length > 3 ? "max-w-[1360px] xl:grid-cols-4" : "max-w-5xl lg:grid-cols-3",
					)}
				>
					{steps.map((step, index) => (
						<div key={index} className="flex max-w-full flex-col justify-between gap-6 rounded-xl bg-white/10 px-4 py-6 md:px-6">
							<div className="space-y-4">
								<p className="flex size-10 shrink-0 items-center justify-center rounded-full bg-white text-lg text-blue-500">{index + 1}</p>
								<h3 className="text-lg font-medium text-white">{step.title}</h3>
								<p className="text-secondary-foreground">{step.description}</p>
							</div>
							{step.url && (
								<div className="w-full">
									<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
								</div>
							)}
							{/* <div className="w-full">
							{step.url ? (
								<img src={step.url} alt={step.title} className="relative w-full rounded-lg border object-cover" loading="lazy" />
							) : (
								<p
									className="text-right text-6xl font-extrabold text-neutral-300 after:content-(--content) md:text-7xl"
									style={{ "--content": `"${index + 1}"` } as React.CSSProperties}
								></p>
							)}
						</div> */}
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
