import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { NoPrefetchLink } from "../ui/custom/no-prefetch-link";

type Feature = {
	title: string;
	description: string;
	image: string;
	imageAlt?: string;
};

export default function FeaturesComponent({ ctaText, ctaUrl, features, badge }: { ctaText: string; ctaUrl: string; features: Feature[]; badge?: string }) {
	return (
		<>
			{features.map((feature, index) => (
				<div key={index} className="container flex flex-col items-center gap-16 px-4 py-24 md:px-6">
					<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-20">
						<div
							className={cn(
								"flex flex-col items-center gap-6 text-center md:items-start md:gap-8 md:text-start",
								index % 2 === 0 ? "md:order-last" : "",
							)}
						>
							<div className="flex flex-col gap-6">
								<h2 className="text-3xl font-semibold text-balance">{feature.title}</h2>
								<p className="text-secondary-foreground text-base">{feature.description}</p>
							</div>
							<NoPrefetchLink
								href={ctaUrl}
								className={cn(buttonVariants({}), "bg-secondary-foreground hover:bg-secondary-foreground/80 rounded-full")}
							>
								{ctaText}
							</NoPrefetchLink>
						</div>
						<div className="bg-muted block w-full overflow-hidden rounded-xl">
							<AspectRatio ratio={3 / 2} className="relative">
								<img src={feature.image} alt={feature.imageAlt ?? feature.title} className="h-full w-full object-cover" loading="lazy" />
								{badge && (
									<div
										className="bg-secondary-foreground/60 absolute top-2 right-2 rounded-sm px-2 py-1 text-xs text-white backdrop-blur-xs after:content-(--content)"
										style={{ "--content": `'${badge}'` } as React.CSSProperties}
									></div>
								)}
							</AspectRatio>
						</div>
					</div>
				</div>
			))}
		</>
	);
}
