import { protectedProcedure } from "../init";
import { UserInfoDB } from "@/@types/user";
import { refreshUser } from "@/server/refresh-user";
import { WEBNAME } from "@/lib/constants";
import { handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";

export const userRouter = {
	// 查询：根据 ID 获取用户
	getUserById: protectedProcedure.handler(async ({ context }) => {
		try {
			const userId = context.sessionUser?.id!;

			const userInfo: UserInfoDB | null = await refreshUser(userId);

			return userInfo;
		} catch (error) {
			handleApiErrorEvent(error, `${WEBNAME} - rpc:  user.getUserById`);
			throw handleUnifiedError(error, "rpc");
		}
	}),
};
