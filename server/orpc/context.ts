// import { NextRequest } from "next/server";
import { getCurrentSessionUser } from "../auth/auth-session";

export async function createRPCContext(headers: Headers) {
	const cfIpCountryCode = headers.get?.("cf-ipcountry") || null;
	const cfIp = headers.get?.("cf-connecting-ip") || null;
	console.log("cfIpCountryCode:", cfIpCountryCode);
	console.log("cfIp:", cfIp);
	const sessionUser = await getCurrentSessionUser();
	console.log("sessionUser:", sessionUser);
	return {
		headers: headers,
		sessionUser,
		cfIpCountryCode,
		cfIp,
	};
}

export type Context = Awaited<ReturnType<typeof createRPCContext>>;
