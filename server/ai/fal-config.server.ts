import { createFalClient, QueueStatus } from "@fal-ai/client";
import { CALLBACK_URL_FAL } from "@/lib/constants";

export const falClient = createFalClient({
	credentials: process.env.FAL_API_KEY,
});

export async function falGenVideoWithWebhook(falAIEndPoint: string, payload: any): Promise<string> {
	const { request_id } = await falClient.queue.submit(falAIEndPoint, {
		input: payload,
		webhookUrl: CALLBACK_URL_FAL,
	});

	return request_id;
}

export async function falGenImage(falAIEndPoint: string, payload: any, waitSubmit = 3000, waitResult = 1000): Promise<string> {
	const { request_id: requestId } = await falClient.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrl: string | null = null;
	await new Promise((resolve) => setTimeout(resolve, waitSubmit));
	while (true) {
		const { status }: QueueStatus = await falClient.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await falClient.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrl = result.data.image.url;
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, waitResult));
	}

	if (!resultUrl) {
		throw new Error("Failed to generate image.");
	}
	return resultUrl;
}

export async function falGenImagesWithQueue(falAIEndPoint: string, payload: any, waitSubmit = 3000, waitResult = 1000): Promise<string[]> {
	const { request_id: requestId } = await falClient.queue.submit(falAIEndPoint, {
		input: payload,
	});

	let resultUrls: string[] | null = null;
	await new Promise((resolve) => setTimeout(resolve, waitSubmit));
	while (true) {
		const { status }: QueueStatus = await falClient.queue.status(falAIEndPoint, {
			requestId: requestId,
		});
		// status: IN_QUEUE, IN_PROGRESS, COMPLETED
		if (status === "COMPLETED") {
			const result = await falClient.queue.result(falAIEndPoint, {
				requestId: requestId,
			});
			resultUrls = result.data.images.map((image: any) => image.url);
			break;
		} else if (status === "IN_PROGRESS" || status === "IN_QUEUE") {
		} else {
			throw new Error(`Failed to generate image.`);
		}

		await new Promise((resolve) => setTimeout(resolve, waitResult));
	}

	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}

export async function falGenImages(falAIEndPoint: string, payload: any): Promise<string[]> {
	const result = await falClient.subscribe(falAIEndPoint, {
		input: payload,
	});

	// Handle different possible response structures
	const resultData = (result as any).data || result;
	if (!resultData.images?.[0]) {
		throw new Error("No image generated");
	}

	const resultUrls: string[] | null = resultData.images.map((image: any) => image.url);
	if (!resultUrls) {
		throw new Error("Failed to generate image.");
	}
	return resultUrls;
}
