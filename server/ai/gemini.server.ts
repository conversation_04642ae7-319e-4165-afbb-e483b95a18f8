import { falGenImages } from "./fal-config.server";

export async function genGemini2_5FromFal(prompt: string, numImages: number, images?: string[]): Promise<string[]> {
	let falAIEndPoint = "fal-ai/nano-banana/edit";
	let payload: any = {
		prompt: prompt,
		num_images: numImages,
	};
	if (images) {
		payload.image_urls = images;
	}
	if (process.env.NODE_ENV === "development") {
		console.log("fal.ai nano-banana payload: ", payload);
		console.log("fal.ai nano-banana fal.ai endpoint: ", falAIEndPoint);
	}

	const resultUrls: string[] = await falGenImages(falAIEndPoint, payload);

	return resultUrls;
}
