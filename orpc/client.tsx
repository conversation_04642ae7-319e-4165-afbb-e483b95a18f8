import { WEBHOST } from "@/lib/constants";
import { createOR<PERSON><PERSON>lient, onError } from "@orpc/client";
import { RPCLink } from "@orpc/client/fetch";
import { RouterClient } from "@orpc/server";
import { AppRouter } from "@/server/orpc/routers";

declare global {
	var $client: RouterClient<AppRouter> | undefined;
}

const link = new RPCLink({
	url: `${WEBHOST}/api/orpc`,
	interceptors: [
		onError((error) => {
			console.error("RPC client error: ", error);
		}),
	],
});

/**
 * Fallback to client-side client if server-side client is not available.
 */
// export const client: RouterClient<AppRouter> = globalThis.$client ?? createORPCClient(link);
export const client: RouterClient<AppRouter> = createORPCClient(link);

import { createTanstackQueryUtils } from "@orpc/tanstack-query";

export const orpc = createTanstackQueryUtils(client);
