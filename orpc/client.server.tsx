// import "server-only";

// import { headers } from "next/headers";
// import { createRouterClient } from "@orpc/server";
// import { appRouter } from "@/server/orpc/routers";
// import { createRPCContext } from "@/server/orpc/context";

// globalThis.$client = createRouterClient(appRouter, {
// 	/**
// 	 * Provide initial context if needed.
// 	 *
// 	 * Because this client instance is shared across all requests,
// 	 * only include context that's safe to reuse globally.
// 	 * For per-request context, use middleware context or pass a function as the initial context.
// 	 */
// 	context: await createRPCContext(await headers()),
// });
