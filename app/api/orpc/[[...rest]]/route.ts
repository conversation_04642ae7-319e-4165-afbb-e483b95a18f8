import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@orpc/server/fetch";
import { NextRequest, NextResponse } from "next/server";
import { appRouter } from "@/server/orpc/routers";
import { createRPCContext } from "@/server/orpc/context";
import { onError } from "@orpc/client";

const handler = new RPCHandler(appRouter, {
	interceptors: [
		onError((error) => {
			console.error("API RPC error: ", error);
			// console.error(error);
		}),
	],
});

async function handleRequest(req: NextRequest) {
	const { response } = await handler.handle(req, {
		prefix: "/api/orpc",
		context: await createRPCContext(req.headers),
	});

	return response ?? new NextResponse("Not found", { status: 404 });
}

export const HEAD = handleRequest;
export const GET = handleRequest;
export const POST = handleRequest;
export const PUT = handleRequest;
export const PATCH = handleRequest;
export const DELETE = handleRequest;
