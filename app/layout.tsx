import "./globals.css";
import { fontHeading, fontSans } from "@/lib/fonts";
import NextTopLoader from "nextjs-toploader";
import type { Metadata } from "next";
import { WEBHOST } from "@/lib/constants";
import { CoreProviders } from "@/components/provider/core-providers";

export const metadata: Metadata = {
	metadataBase: new URL(WEBHOST),
	openGraph: {
		// title: `${WEBNAME}`,
		type: "website",
		// url: WEBHOST,
		// description: "",
		// images: `${OSS_URL_HOST}/mkt/og-image.webp`,
	},
	twitter: {
		// site: WEBHOST,
	},
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
	return (
		<html lang="en" suppressHydrationWarning className={fontSans.className}>
			<head>
				<link rel="icon" href="/favicon.ico" sizes="any" />
			</head>

			<body className={`${fontHeading.variable} overscroll-none`}>
				<NextTopLoader color="#1a1a19" initialPosition={0.3} speed={600} crawlSpeed={200} showSpinner={false} shadow={false} />
				<CoreProviders>{children}</CoreProviders>
			</body>
		</html>
	);
}
