import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BookText, FolderTree, Logs } from "lucide-react";
import { DashHeader } from "./_components/admin-navigate";
import { NoPrefetchLink } from "@/components/ui/custom/no-prefetch-link";

export default async function Page() {
	return (
		<div className="flex h-full flex-col">
			<DashHeader current={{ title: "Admin" }} />
			<div className="flex-1 overflow-auto p-4">
				<Card className="border-none shadow-none">
					<CardHeader className="pb-8">
						<CardTitle className="text-2xl font-bold">Content Management</CardTitle>
						<CardDescription className="text-muted-foreground">Manage your blog content, changelog.</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="grid gap-4 md:grid-cols-3">
							<Card className="hover:bg-accent transition-colors">
								<CardContent className="pt-6">
									<Button asChild variant="ghost" className="w-full justify-start" size="lg">
										<NoPrefetchLink href="/admin/blog/category" className="flex items-center gap-2">
											<FolderTree className="h-5 w-5" />
											<span>Blog Category</span>
										</NoPrefetchLink>
									</Button>
								</CardContent>
							</Card>
							<Card className="hover:bg-accent transition-colors">
								<CardContent className="pt-6">
									<Button asChild variant="ghost" className="w-full justify-start" size="lg">
										<NoPrefetchLink href="/admin/blog" className="flex items-center gap-2">
											<BookText className="h-5 w-5" />
											<span>Blogs</span>
										</NoPrefetchLink>
									</Button>
								</CardContent>
							</Card>
							<Card className="hover:bg-accent transition-colors">
								<CardContent className="pt-6">
									<Button asChild variant="ghost" className="w-full justify-start" size="lg">
										<NoPrefetchLink href="/admin/changelog" className="flex items-center gap-2">
											<Logs className="h-5 w-5" />
											<span>Changelog</span>
										</NoPrefetchLink>
									</Button>
								</CardContent>
							</Card>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
