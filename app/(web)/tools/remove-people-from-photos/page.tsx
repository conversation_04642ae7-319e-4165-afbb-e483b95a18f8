import type { Metadata } from "next";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import FAQsComponent from "@/components/landing/faqs";
import FinalCTA from "@/components/landing/final-cta";
import FeaturesComponent from "@/components/landing/features";
import RemovePeopleClient from "./remove-people.client";
import { HowToUseBox } from "@/components/landing/how-to-use-box";
import BreadcrumbPath from "@/components/landing/breadcrumb-path";

export const metadata: Metadata = {
	title: `Remove People from Photos Online with AI | ${WEBNAME}`,
	description:
		"Easily remove a person, photobomber, or crowd from any photo with our AI person remover. Just upload your picture, type who to remove, and get a clean image in seconds.",
	alternates: {
		canonical: "/tools/remove-people-from-photos",
	},
};

export default async function Page() {
	return (
		<main className="min-h-screen pb-24">
			<div className="min-h-[calc(100vh-160px)] px-4 pb-24 md:px-6">
				<div className="container px-0 pt-3">
					<BreadcrumbPath
						paths={[
							{ title: "Home", href: "/" },
							{ title: "Image Editor", href: "/edit" },
						]}
						current={"Remove People from Photos"}
					/>
				</div>
				<div className="container flex h-full flex-col rounded-2xl px-0 md:rounded-4xl">
					<div className="flex w-full grow flex-col items-center pt-16 md:grow-0">
						<div className="mx-auto flex w-full max-w-4xl flex-col items-center gap-y-4 text-center">
							<h1 className="text-[32px] leading-9 font-medium md:text-[48px] md:leading-16 md:font-semibold">
								Remove People from Photos with AI
							</h1>
							<div className="text-secondary-foreground text-base">
								Make any unwanted person vanish with just a few words. Simply describe who you want to remove, and our AI will make them
								disappear in the blink of an eye, leaving a flawless photo with no trace behind.
							</div>
						</div>
						<div className="mt-10 w-full max-w-5xl md:mt-14">
							<RemovePeopleClient />
						</div>
					</div>
				</div>
			</div>

			<HowToUseBox
				title="How to remove people from pictures"
				steps={[
					{
						title: "Upload your image",
						description:
							"Choose the photo you want to perfect and upload it directly from your phone, camera, or computer. No special software or downloads required.",
					},
					{
						title: "Describe the unwanted people",
						description:
							"Type in who you’d like removed—whether it’s a random stranger, a tourist in the background, or a photobomber who slipped into your shot. The AI will understand and prepare to make your photo look just the way you want.",
					},
					{
						title: "Download and share",
						description:
							"Within seconds, your photo comes back natural, clean, and distraction-free. Download it instantly and share it anywhere with confidence.",
					},
				]}
			/>

			<FeaturesComponent
				ctaText="Remove people now"
				ctaUrl="#"
				features={[
					{
						title: "Reclaim your perfect photo every time",
						description:
							"We have all taken a photo we loved, only to notice later that someone in the background has distracted from the moment. It’s frustrating, and sometimes it feels like that perfect shot is ruined forever. With Editpal’s Remove People from Photos tool, you can get it back exactly the way you pictured it. Simply describe who you want removed, and our AI will work in seconds to restore your scene so naturally that no one will ever know they were there. No complicated steps, no editing skills needed—just your perfect moment, saved.",
						image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/feature-1.webp`,
						imageAlt: `Remove people in the background`,
					},
					{
						title: "Preserve the beauty of your travel memories",
						description:
							"Your travel photos should remind you of the breathtaking places you visited—not the crowds of tourists who happened to be there. Whether you are standing before a famous landmark or watching a sunset on a quiet beach, Editpal helps you remove people from travel photos and reveal the view as you truly experienced it. Our smart AI blends the background seamlessly, preserving every texture, shadow, and color. The result feels untouched and authentic, leaving you with a memory as beautiful as the moment itself.",
						image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/feature-2.webp`,
						imageAlt: `Remove all people from image`,
					},
					{
						title: "Keep the memories you love without the faces you do not",
						description:
							"Some photos are precious because of the memory they hold, even if they also include someone you would rather not see anymore. Maybe it is an old group shot after a breakup, or a family gathering where not everyone wants to be shown publicly. With Editpal, you can remove someone from a photo without losing the joy of the rest of the image. This is about protecting your story, honoring the moments you care about, and making sure your photos reflect your life the way you want to share it.",
						image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/feature-3.webp`,
						imageAlt: `Remove a person from a photo`,
					},
					{
						title: "Create natural results with AI you can trust",
						description:
							"When you remove a person from a photo, you deserve more than just an empty space where they used to be. Editpal’s AI understands light, texture, and perspective to recreate the background perfectly. Whether you are cleaning up a product photo, polishing images for social media, or simply removing distractions, the results look professional and authentic on any device. And because we protect your privacy, your photos stay secure while looking like they were never edited at all.",
						image: `${OSS_URL_HOST}/mkt/pages/tools/remove-people-from-photos/feature-4.webp`,
						imageAlt: `Remove people from photos`,
					},
				]}
			/>

			<FAQsComponent
				title="Frequently asked questions"
				faqs={[
					{
						question: "Why do I want to remove a person from a photo?",
						answer: "Sometimes a single unwanted face can completely change how a photo makes you feel. Maybe it’s a stranger who walked into your perfect travel shot, a photobomber ruining an otherwise perfect memory, or someone you no longer want in your personal or professional images. Removing a person from a photo lets you protect your memories, control your privacy, and keep only what truly matters in the frame.",
					},
					{
						question: "How can I remove a person from a group photo?",
						answer: "With Editpal’s ‘Remove People from Photos’ tool, it’s as simple as selecting your image, typing in a short description of the person you want to remove, and letting our AI handle the rest. In seconds, the unwanted person is gone and the background is seamlessly restored—so your group photo still looks natural and complete.",
					},
					{
						question: "How do I remove people in the background?",
						answer: "Simply upload your photo, describe the unwanted people in the background, and click remove. Editpal’s AI background person remover understands context, so it erases them cleanly while keeping the scene intact—whether it’s tourists in your travel shot or passersby in a street image.",
					},
					{
						question: "How can I remove all people from a picture?",
						answer: "Yes—you can remove every person in a photo with just one prompt. Just tell Editpal, ‘Remove all people from this photo,’ and the AI will instantly clean up the shot, leaving you with an empty, distraction‑free scene.",
					},
					{
						question: "Can I remove unwanted people from my travel photos?",
						answer: "Absolutely. If crowded tourist spots ruined your perfect travel pictures, Editpal can instantly remove tourists, strangers, or guides, restoring your photo to look like you had the whole place to yourself.",
					},
					{
						question: "If I remove a person from the background, will it affect the rest of the photo?",
						answer: "No—our AI is designed to blend the background perfectly after removing someone. It fills in the scene so naturally that no one would guess anyone was ever there, keeping lighting, colors, and details consistent.",
					},
					{
						question: "How long does it take to remove a person from a photo?",
						answer: "In most cases, just seconds. Editpal’s AI works fast, so you can upload, remove, and download your updated photo almost instantly—without sacrificing quality.",
					},
					{
						question: "Can I keep the person in the photo but remove the background instead?",
						answer: "Yes. While ‘Remove People from Photos’ focuses on removing people, Editpal also has tools for removing or replacing backgrounds. You can isolate a person and place them on a clean, distraction‑free backdrop whenever you want.",
					},
					{
						question: "What is the best app to remove a person from a photo?",
						answer: "Editpal is built for exactly that. Unlike generic editors, our AI tool is designed to remove people from photos with precision and realism—no complex skills needed, no obvious edits, and results you can trust every time.",
					},
				]}
			/>

			<FinalCTA
				className=""
				title="Remove people from any photo—No photoshop needed"
				description="Instantly clear away unwanted people, strangers in the background, or accidental photobombers. Each image transforms into a clean, distraction-free memory that feels true to the moment you lived."
				ctaText="Remove people now"
				ctaTextDisplay={true}
				ctaUrl="#"
			/>
		</main>
	);
}
