import { WEBNAME } from "@/lib/constants";
import type { Metada<PERSON> } from "next";
import { ConfirmationClient } from "./confirmation.client";
import { Suspense } from "react";

export const metadata: Metadata = {
	title: `Confirmation | ${WEBNAME}`,
	alternates: {
		canonical: "/user/confirmation",
	},
	robots: {
		index: false,
		follow: false,
	},
};
export default function Page() {
	return (
		<div className="flex min-h-screen flex-col items-center justify-center p-6">
			<Suspense fallback={<div>Loading...</div>}>
				<ConfirmationClient />
			</Suspense>
		</div>
	);
}
