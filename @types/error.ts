import { ORPCError } from "@orpc/server";

// error code number
export enum ERROR_CODE {
	BAD_REQUEST_PARAMS = 400,
	UNAUTHORIZED = 401,
	PAYMENT_REQUIRED = 402,
	NOT_FOUND = 404,
	METHOD_NOT_SUPPORTED = 405,
	UNPROCESSABLE_CONTENT = 422,
	INTERNAL_SERVER_ERROR = 500,
	NOT_IMPLEMENTED = 501,
}

export class ParamsError extends Error {
	// 400：请求参数不足/不合规
	public readonly status: number = ERROR_CODE.BAD_REQUEST_PARAMS;

	constructor(message: string) {
		super(message);
		this.name = "ParamsError";
	}
}
export class AuthError extends Error {
	// 401：未登录/未授权
	public readonly status: number = ERROR_CODE.UNAUTHORIZED;

	constructor(message: string) {
		super(message);
		this.name = "AuthError";
	}
}
export class Credits402Error extends Error {
	// 402：剩余token数量不足
	public readonly status: number = ERROR_CODE.PAYMENT_REQUIRED;

	constructor(message: string) {
		super(message);
		this.name = "Credits402Error";
	}
}
export class NotFoundError extends Error {
	// 404：未找到
	public readonly status: number = ERROR_CODE.NOT_FOUND;

	constructor(message: string) {
		super(message);
		this.name = "NotFoundError";
	}
}
export class MethodNotSupportedError extends Error {
	// 405: 服务器在fetch时遇到了未预料的情况
	public readonly status: number = ERROR_CODE.METHOD_NOT_SUPPORTED;

	constructor(message: string) {
		super(message);
		this.name = "MethodNotSupportedError";
	}
}
export class UnprocessableError extends Error {
	// 422：请求参数不正确
	public readonly status: number = ERROR_CODE.UNPROCESSABLE_CONTENT;

	constructor(message: string) {
		super(message);
		this.name = "UnprocessableError";
	}
}
export class ServerError extends Error {
	// 500：服务器在处理请求时遇到了未预料的情况
	public readonly status: number = ERROR_CODE.INTERNAL_SERVER_ERROR;

	constructor(message: string) {
		super(message);
		this.name = "ServerError";
	}
}
export class NotImplementedError extends Error {
	// 501：服务器不支持当前请求所需要的某个功能
	public readonly status: number = ERROR_CODE.NOT_IMPLEMENTED;

	constructor(message: string) {
		super(message);
		this.name = "NOT_IMPLEMENTED";
	}
}

export class IgnoreError extends Error {
	constructor(message: string) {
		super(message);
		this.name = "IgnoreError";
	}
}

export const handleError = (status: number | undefined | null, message: string | undefined | null) => {
	if (!status) return;

	if (status === 200) return;

	switch (status) {
		case ERROR_CODE.BAD_REQUEST_PARAMS:
			throw new ParamsError(message as string);
		case ERROR_CODE.UNAUTHORIZED:
			throw new AuthError(message as string);
		case ERROR_CODE.PAYMENT_REQUIRED:
			throw new Credits402Error(message as string);
		case ERROR_CODE.NOT_FOUND:
			throw new NotFoundError(message as string);
		case ERROR_CODE.METHOD_NOT_SUPPORTED:
			throw new MethodNotSupportedError(message as string);
		case ERROR_CODE.UNPROCESSABLE_CONTENT:
			throw new UnprocessableError(message as string);
		case ERROR_CODE.INTERNAL_SERVER_ERROR:
			throw new ServerError(message as string);
		case ERROR_CODE.NOT_IMPLEMENTED:
			throw new NotImplementedError(message as string);
		default:
			throw new Error(message as string);
	}
};

// ---------------- RPC 错误转换功能 ----------------

// 普通错误转换为 RPC 错误
export const transformErrorToRPC = (error: Error) => {
	// 如果已经是 RPCError，直接返回
	if (error instanceof ORPCError) {
		return error;
	}

	let code = "INTERNAL_SERVER_ERROR";
	let status = 500;
	let message = error.message || "Unknown error";

	// 根据错误类型映射到对应的 RPC 错误码
	if (error instanceof ParamsError) {
		code = "BAD_REQUEST";
		status = 400;
	} else if (error instanceof AuthError) {
		code = "UNAUTHORIZED";
		status = 401;
	} else if (error instanceof Credits402Error) {
		code = "PAYMENT_REQUIRED";
		status = 402;
	} else if (error instanceof NotFoundError) {
		code = "NOT_FOUND";
		status = 404;
	} else if (error instanceof MethodNotSupportedError) {
		code = "METHOD_NOT_SUPPORTED";
		status = 405;
	} else if (error instanceof UnprocessableError) {
		code = "UNPROCESSABLE_CONTENT";
		status = 422;
	} else if (error instanceof ServerError) {
		code = "INTERNAL_SERVER_ERROR";
		status = 500;
	} else if (error instanceof NotImplementedError) {
		code = "NOT_IMPLEMENTED";
		status = 501;
	}
	return new ORPCError(code, {
		status,
		message,
	});
};

// RPC 错误转换为普通错误（改进版本）
export const transformRPCErrorToError = (error: any): Error => {
	console.log("RPC error code: ", error.code);
	// 检查是否是 RPCError 实例
	if (error instanceof ORPCError) {
		const code = error.code;
		const message = error.message || "Unknown error";

		switch (code) {
			case "BAD_REQUEST":
				return new ParamsError(message);
			case "UNAUTHORIZED":
				return new AuthError(message);
			case "PAYMENT_REQUIRED":
				return new Credits402Error(message);
			case "NOT_FOUND":
				return new NotFoundError(message);
			case "METHOD_NOT_SUPPORTED":
				return new MethodNotSupportedError(message);
			case "UNPROCESSABLE_CONTENT":
				return new UnprocessableError(message);
			case "INTERNAL_SERVER_ERROR":
				return new ServerError(message);
			case "NOT_IMPLEMENTED":
				return new NotImplementedError(message);
			default:
				return new ServerError(message);
		}
	}

	// 如果不是 RPC 错误，直接返回原错误
	return error instanceof Error ? error : new Error(String(error));
};

// 统一的错误处理函数，可以处理任何类型的错误
export const handleUnifiedError = (error: any, preferType: "rpc" | "error" = "rpc") => {
	if (preferType === "rpc") {
		// 优先返回 RPC 错误
		if (error instanceof ORPCError) {
			return error;
		}
		console.log("transformErrorToRPC");
		return transformErrorToRPC(error);
	} else {
		// 优先返回普通错误
		if (error instanceof Error && !(error instanceof ORPCError)) {
			return error;
		}
		return transformRPCErrorToError(error);
	}
};
