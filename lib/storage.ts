interface ImageTransform {
	scale: number;
	x: number;
	y: number;
	rotation: number;
	cropBox?: {
		x: number;
		y: number;
		width: number;
		height: number;
	};
}

interface CanvasElement {
	id: string;
	type: "image" | "text" | "shape" | "video";
	transform: ImageTransform;
	zIndex: number;
	width?: number;
	height?: number;
	// OSS相关字段
	ossUrl?: string; // OSS URL for images
	status?: "uploading" | "generating" | "completed";
}

interface CanvasState {
	elements: CanvasElement[];
	backgroundColor?: string;
	lastModified: number;
	viewport?: {
		x: number;
		y: number;
		scale: number;
	};
}

class CanvasStorage {
	private readonly STATE_KEY = "canvas-state";

	// Save canvas state to localStorage
	saveCanvasState(state: CanvasState): void {
		try {
			localStorage.setItem(this.STATE_KEY, JSON.stringify(state));
		} catch (e) {
			console.error("Failed to save canvas state:", e);
		}
	}

	// Load canvas state from localStorage
	getCanvasState(): CanvasState | null {
		try {
			const stored = localStorage.getItem(this.STATE_KEY);
			return stored ? JSON.parse(stored) : null;
		} catch (e) {
			console.error("Failed to load canvas state:", e);
			return null;
		}
	}

	// Clear all stored data
	clearAll(): void {
		localStorage.removeItem(this.STATE_KEY);
	}
}

export const canvasStorage = new CanvasStorage();
export type { CanvasState, CanvasElement, ImageTransform };
