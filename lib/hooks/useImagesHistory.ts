import { useCallback, useMemo, useRef, useState } from "react";
import type { PlacedImage } from "@/@types/canvas";

interface CommitOptions {
	label?: string;
}

interface UpdateOptions extends CommitOptions {
	commit?: boolean; // default true when label provided in convenience wrapper
}

interface HistoryEntry {
	images: PlacedImage[];
	label?: string;
}

export function useImagesHistory(initial: PlacedImage[] = []) {
	const [images, _setImages] = useState<PlacedImage[]>(initial);
	const [history, setHistory] = useState<HistoryEntry[]>([{ images: initial }]);
	const [historyIndex, setHistoryIndex] = useState<number>(0);

	// keep a ref to always read latest state inside callbacks
	const imagesRef = useRef(images);
	imagesRef.current = images;

	const canUndo = historyIndex > 0;
	const canRedo = historyIndex < history.length - 1;

	//  一个不会创建历史记录的更新函数。适用于一些临时性的、不需要被记录的操作（比如在拖拽过程中实时更新图片位置，但只有在拖拽结束时才创建一次历史记录）。
	const setImages: React.Dispatch<React.SetStateAction<PlacedImage[]>> = useCallback((updater) => {
		_setImages((prev) => {
			const next = typeof updater === "function" ? (updater as (p: PlacedImage[]) => PlacedImage[])(prev) : updater;
			return next;
		});
	}, []);

	// 重置整个状态，清空历史记录，回到初始状态。
	const reset = useCallback((next: PlacedImage[]) => {
		_setImages(next);
		setHistory([{ images: [...next] }]);
		setHistoryIndex(0);
	}, []);

	// 接收一个函数来更新 images 状态，并且可以选择是否提交到历史记录。这是最常用的更新方式之一。
	const update = useCallback(
		(updateFn: (prev: PlacedImage[]) => PlacedImage[], opts?: UpdateOptions) => {
			let newState: PlacedImage[];
			_setImages((prev) => {
				newState = updateFn(prev);
				return newState;
			});
			if (opts?.commit) {
				// 直接使用计算出的新状态，而不是依赖imagesRef
				queueMicrotask(() => {
					setHistory((prev) => {
						const trunc = prev.slice(0, historyIndex + 1);
						const nextEntry: HistoryEntry = { images: [...newState], label: opts?.label };
						const next = [...trunc, nextEntry];
						return next;
					});
					setHistoryIndex((idx) => idx + 1);
				});
			}
		},
		[historyIndex],
	);

	const undo = useCallback(() => {
		if (!canUndo) return;
		const target = history[historyIndex - 1];
		_setImages(target.images);
		setHistoryIndex(historyIndex - 1);
	}, [canUndo, history, historyIndex]);

	const redo = useCallback(() => {
		if (!canRedo) return;
		const target = history[historyIndex + 1];
		_setImages(target.images);
		setHistoryIndex(historyIndex + 1);
	}, [canRedo, history, historyIndex]);

	const historyLength = history.length;

	return useMemo(
		() => ({
			images,
			setImages, // non-committing set
			update, // functional update, optionally commit
			reset,
			undo,
			redo,
			canUndo,
			canRedo,
			historyIndex,
			historyLength,
		}),
		[images, setImages, update, reset, undo, redo, canUndo, canRedo, historyIndex, historyLength],
	);
}
